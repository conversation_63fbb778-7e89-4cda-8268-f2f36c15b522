import 'package:flutter/material.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';
import '../../models/ongoing_session.dart';
import '../../services/ongoing_sessions_service.dart';
import '../../utils/app_themes.dart';

/// Screen displaying list of active charging sessions
class ActiveSessionsScreen extends StatefulWidget {
  const ActiveSessionsScreen({super.key});

  @override
  State<ActiveSessionsScreen> createState() => _ActiveSessionsScreenState();
}

class _ActiveSessionsScreenState extends State<ActiveSessionsScreen> {
  final OngoingSessionsService _ongoingSessionsService =
      OngoingSessionsService();
  List<OngoingSession> _activeSessions = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadActiveSessions();
  }

  Future<void> _loadActiveSessions() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _ongoingSessionsService.getOngoingSessions();

      if (response != null && response.success) {
        setState(() {
          _activeSessions = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to load active sessions';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading sessions: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshSessions() async {
    await _loadActiveSessions();
  }

  void _onSessionTapped(OngoingSession session) {
    debugPrint('🔋 ===== SESSION CARD TAPPED =====');
    debugPrint('🔋 Session ID (Transaction ID): ${session.id}');
    debugPrint('🔋 Authorization Reference: ${session.authorizationReference}');
    debugPrint('🔋 Charger UID: ${session.chargerUid}');
    debugPrint('🔋 Connector ID: ${session.connectorId}');
    debugPrint('🔋 Station Name: ${session.charger.chargerName}');
    debugPrint('🔋 Status: ${session.status}');
    debugPrint('🔋 Invoice Number: ${session.invoiceNumber}');
    debugPrint('🔋 ===== NAVIGATION DATA =====');
    debugPrint('🔋 Transaction ID for Stop API: ${session.id}');
    debugPrint('🔋 Transaction ID Type: ${session.id.runtimeType}');
    debugPrint(
        '🔋 Authorization Reference for Data Polling: ${session.authorizationReference}');
    debugPrint('🔋 Expected Stop Endpoint: /user/sessions/stop/${session.id}');
    debugPrint('🔋 Expected Data Polling Endpoint: /user/sessions/on-going-data?authorization_reference=${session.authorizationReference}');

    // Navigate to charging session screen with the authorization reference as the primary identifier
    Navigator.pushNamed(
      context,
      '/charging_session',
      arguments: {
        'station_uid': session.chargerUid,
        'connector_id': session.connectorId,
        'charge_percentage': 0.0, // Will be updated by real-time data polling
        'verified_session_data': {
          // The authorization_reference is the key identifier for data polling
          'authorization_reference': session.authorizationReference,
          // CRITICAL: Use the 'id' field as transaction_id for stop transaction API
          'id': session.id, // This is the actual transaction ID (e.g., 30964)
          'transaction_id':
              session.id.toString(), // String version for compatibility
          'charger_uid': session.chargerUid,
          'connector_id': session.connectorId,
          'invoice_number': session.invoiceNumber,
          'status': session.status,
          'charger_name': session.charger.chargerName,
          'charging_rate': session.evChargingRate,
          'booking_id': session.bookingId,
          'created_at': session.createdAt.toIso8601String(),
          // Mark this as an ongoing session (not a new session)
          'is_ongoing_session': true,
        },
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? const Color(0xFF121212) : Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Active Charging Sessions',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        foregroundColor: isDarkMode ? Colors.white : Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshSessions,
            tooltip: 'Refresh Sessions',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppThemes.primaryColor),
            SizedBox(height: 16),
            Text(
              'Loading active sessions...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshSessions,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_activeSessions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.battery_charging_full,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'No Active Charging Sessions',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start charging at any station to see your active sessions here.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshSessions,
      color: AppThemes.primaryColor,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _activeSessions.length,
        itemBuilder: (context, index) {
          final session = _activeSessions[index];
          return _buildSessionCard(session);
        },
      ),
    );
  }

  Widget _buildSessionCard(OngoingSession session) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: LiquidGlass(
        settings: LiquidGlassSettings(
          thickness: 18,
          glassColor:
              isDarkMode ? const Color(0x18FFFFFF) : const Color(0x12000000),
          lightIntensity: 1.8,
          ambientStrength: 0.4,
          blend: 40,
          lightAngle: 1.0,
        ),
        shape: LiquidRoundedSuperellipse(
          borderRadius: Radius.circular(16),
        ),
        glassContainsChild: true,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _onSessionTapped(session),
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withValues(alpha: 0.05)
                    : Colors.white.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.black.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppThemes.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.ev_station,
                          color: AppThemes.primaryColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              session.charger.chargerName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Connector ${session.connectorId}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(session.status)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          session.status.toUpperCase(),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _getStatusColor(session.status),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          'Charging Rate',
                          '${session.evChargingRate} kW',
                          Icons.flash_on,
                        ),
                      ),
                      Expanded(
                        child: _buildInfoItem(
                          'Session ID',
                          '#${session.id}',
                          Icons.confirmation_number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoItem(
                          'Started',
                          _formatDateTime(session.createdAt),
                          Icons.access_time,
                        ),
                      ),
                      Expanded(
                        child: _buildInfoItem(
                          'Invoice',
                          session.invoiceNumber,
                          Icons.receipt,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'charging':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'completed':
        return Colors.blue;
      case 'error':
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
