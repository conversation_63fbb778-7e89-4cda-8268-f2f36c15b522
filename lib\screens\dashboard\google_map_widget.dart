import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart' as geo;

import '../../services/persistent_marker_service.dart'; // Import the PersistentMarkerService
import '../../providers/dashboard_notifier.dart';
import '../../providers/theme_provider.dart'; // Import theme provider
import '../../config/google_maps_styles.dart'; // Import map styles

class GoogleMapWidget extends ConsumerStatefulWidget {
  final void Function(LatLng)? onTap;
  final List<Map<String, dynamic>>? stations;
  final Function(Map<String, dynamic>)? onStationSelected;
  final Function(double, double)? onLocationUpdated;
  final Function(CameraPosition)? onCameraPositionChanged;
  final double? initialLatitude;
  final double? initialLongitude;
  final double? initialZoom;
  final bool showLocationButton;
  final Set<Polyline>? polylines;
  final LatLngBounds? fitBounds;
  final Set<Marker>? additionalMarkers;
  final Function(String)? onPolylineTapped; // New callback for polyline taps

  const GoogleMapWidget({
    super.key,
    this.onTap,
    this.stations,
    this.onStationSelected,
    this.onLocationUpdated,
    this.onCameraPositionChanged,
    this.initialLatitude,
    this.initialLongitude,
    this.initialZoom,
    this.showLocationButton = true,
    this.polylines,
    this.fitBounds,
    this.additionalMarkers,
    this.onPolylineTapped,
  });

  @override
  ConsumerState<GoogleMapWidget> createState() => GoogleMapWidgetState();
}

class GoogleMapWidgetState extends ConsumerState<GoogleMapWidget> {
  // Google Maps controller
  final Completer<GoogleMapController> _controller = Completer();
  GoogleMapController? _mapController;

  // Map state
  bool _isMapInitialized = false;
  Set<Marker> _markers = {};

  // Location state
  bool _locationPermissionGranted = false;
  bool _isLoadingLocation = false;

  // Currently selected station ID
  String? _selectedStationId;

  // Location tracking
  StreamSubscription<geo.Position>? _positionStreamSubscription;
  bool _isLocationTrackingEnabled = false;

  // Custom user location marker with navigation support
  Marker? _userLocationMarker;
  geo.Position? _currentUserPosition;
  double _currentBearing = 0.0; // Current heading/direction for car rotation

  // Pulsing animation for car marker
  Timer? _pulseTimer;
  double _currentPulseRadius = 0.0;

  // Auto-centering control flags
  bool _hasShownInitialLocation = false;
  bool _userHasInteractedWithMap = false;
  DateTime? _lastCameraAnimation;

  // Use PersistentMarkerService directly for robust marker handling
  final _persistentMarkerService = PersistentMarkerService();

  // Theme-related state
  bool _isDarkMode = false;
  String? _currentMapStyle;

  @override
  void initState() {
    super.initState();
    _initializeTheme();
    _checkLocationPermission();
    _initializeMap();
    _initializeMarkerService();
    _updateMarkers();
  }

  // Initialize theme state
  void _initializeTheme() {
    // Get initial theme state
    final themeNotifier = ref.read(themeNotifierProvider.notifier);
    _isDarkMode = themeNotifier.isDarkMode;
    _currentMapStyle = GoogleMapsStyles.getMapStyle(_isDarkMode);
  }

  // Initialize the marker service
  Future<void> _initializeMarkerService() async {
    await _persistentMarkerService.initialize();
  }

  void _initializeMap() async {
    // Map will be initialized with coordinates provided by the parent widget
    // No need to override with hardcoded coordinates here
    if (_mapController != null) {
      if (mounted) {
        setState(() {
          _isMapInitialized = true;
        });
      }

      // We don't automatically get user location on map initialization
      // The parent widget will handle location updates and smooth transitions
    }
  }

  @override
  void didUpdateWidget(GoogleMapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if polylines have changed and force rebuild if needed
    if (oldWidget.polylines != widget.polylines) {
      if (mounted) {
        setState(() {
          // Force rebuild when polylines change
        });
      }
    }

    // Update markers if stations changed
    if (widget.stations != oldWidget.stations) {
      _updateMarkers();
    }
  }

  @override
  void dispose() {
    // Cancel location tracking subscription when widget is disposed
    _stopLocationTracking();

    // Stop pulsing animation
    _stopPulsingAnimation();

    // Dispose of the map controller
    _mapController?.dispose();

    // Note: We don't dispose the PersistentMarkerService because it's a singleton
    // and might be used by other components. This helps maintain the cache.

    super.dispose();
  }

  // Update map style when theme changes
  void _updateMapTheme(bool isDarkMode) {
    if (_isDarkMode == isDarkMode) return; // No change needed

    if (mounted) {
      setState(() {
        _isDarkMode = isDarkMode;
        _currentMapStyle = GoogleMapsStyles.getMapStyle(_isDarkMode);
      });
    }
  }

  // Check if location permission is granted
  Future<void> _checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await geo.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('Location services are disabled');
        return;
      }

      // Check location permission
      geo.LocationPermission permission =
          await geo.Geolocator.checkPermission();
      if (permission == geo.LocationPermission.denied) {
        permission = await geo.Geolocator.requestPermission();
        if (permission == geo.LocationPermission.denied) {
          debugPrint('Location permissions are denied');
          return;
        }
      }

      if (permission == geo.LocationPermission.deniedForever) {
        return;
      }

      // Permission granted
      if (mounted) {
        setState(() {
          _locationPermissionGranted = true;
        });
      }

      // Start location tracking if map is initialized
      if (_isMapInitialized) {
        _startLocationTracking();
        // Also get current location immediately to show the car marker
        _getCurrentLocationAndShowMarker();
      }
    } catch (e) {
      debugPrint('Error checking location permission: $e');
    }
  }

  // Start tracking user's location
  void _startLocationTracking() {
    if (_isLocationTrackingEnabled || !_locationPermissionGranted) {
      return;
    }

    try {
      const locationSettings = geo.LocationSettings(
        accuracy: geo.LocationAccuracy.high,
        distanceFilter:
            50, // Increased from 10 to 50 meters to reduce frequency
      );

      _positionStreamSubscription = geo.Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen((geo.Position position) {
        if (mounted) {
          // Calculate bearing/heading for car rotation (like Google Maps live location)
          double newBearing = _currentBearing;

          // First, try to use device heading if available (more accurate for navigation)
          if (position.heading >= 0) {
            newBearing = position.heading;
          } else if (_currentUserPosition != null) {
            // Fallback to calculated bearing between positions
            newBearing = geo.Geolocator.bearingBetween(
              _currentUserPosition!.latitude,
              _currentUserPosition!.longitude,
              position.latitude,
              position.longitude,
            );
            // Only update bearing if movement is significant (> 5 meters)
            double distance = geo.Geolocator.distanceBetween(
              _currentUserPosition!.latitude,
              _currentUserPosition!.longitude,
              position.latitude,
              position.longitude,
            );
            if (distance > 5.0) {
            } else {
              newBearing =
                  _currentBearing; // Keep previous bearing for small movements
            }
          }

          // Update current bearing
          _currentBearing = newBearing;

          // Store current position for next bearing calculation
          _currentUserPosition = position;

          // Update custom user location marker with car icon and rotation
          _updateUserLocationMarker(position);
        }

        // Only notify parent of location update if we haven't shown initial location yet
        // or if user hasn't manually interacted with the map
        if (widget.onLocationUpdated != null &&
            (!_hasShownInitialLocation || !_userHasInteractedWithMap)) {
          widget.onLocationUpdated!(position.latitude, position.longitude);

          // Mark that we've shown the initial location
          if (!_hasShownInitialLocation) {
            _hasShownInitialLocation = true;
          }
        }
      });

      _isLocationTrackingEnabled = true;

      // Start pulsing animation for car marker
      _startPulsingAnimation();
    } catch (e) {
      // Error starting location tracking
    }
  }

  // Stop tracking user's location
  void _stopLocationTracking() {
    if (_positionStreamSubscription != null) {
      _positionStreamSubscription!.cancel();
      _positionStreamSubscription = null;
      _isLocationTrackingEnabled = false;
    }

    // Stop pulsing animation
    _stopPulsingAnimation();
  }

  // Start pulsing animation for car marker (like Google Maps live location)
  void _startPulsingAnimation() {
    // Stop any existing timer
    _stopPulsingAnimation();

    // Create a timer that updates the pulse radius every 100ms
    _pulseTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      // Calculate pulse radius using sine wave for smooth animation (adjusted for smaller circle)
      final double time = timer.tick * 0.1; // Slow down the animation
      _currentPulseRadius = 20.0 +
          (math.sin(time) *
              12.0); // Pulse between 8-32 radius (adjusted for smaller car icon)

      // Update the car marker with new pulse radius
      if (_currentUserPosition != null) {
        _updateUserLocationMarker(_currentUserPosition!);
      }
    });
  }

  // Stop pulsing animation
  void _stopPulsingAnimation() {
    if (_pulseTimer != null) {
      _pulseTimer!.cancel();
      _pulseTimer = null;
      _currentPulseRadius = 0.0;
    }
  }

  // Method to focus on a specific location with debouncing
  Future<void> focusOnLocation(double latitude, double longitude,
      [double zoom = 15.0]) async {
    if (_mapController == null) {
      return;
    }

    // Debounce camera animations to prevent conflicts
    final now = DateTime.now();
    if (_lastCameraAnimation != null &&
        now.difference(_lastCameraAnimation!).inMilliseconds < 1000) {
      return;
    }

    try {
      _lastCameraAnimation = now;

      // Animate to the specified location
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(latitude, longitude),
            zoom: zoom,
          ),
        ),
      );
      debugPrint('Focused on location: $latitude, $longitude with zoom $zoom');
    } catch (e) {
      debugPrint('Error focusing on location: $e');
    }
  }

  // Add a custom marker to the map
  void addCustomMarker(Marker marker) {
    if (mounted) {
      setState(() {
        // Create a new set with all existing markers plus the new one
        final newMarkers = Set<Marker>.from(_markers);

        // Remove any existing marker with the same ID
        newMarkers.removeWhere((m) => m.markerId == marker.markerId);

        // Add the new marker
        newMarkers.add(marker);

        // Update the markers
        _markers = newMarkers;
      });
    }
  }

  // Method to focus on user's current location
  void focusOnUserLocation() {
    handleLocationButtonPress();
  }

  // Public method to select a station from external calls
  void selectStation(Map<String, dynamic> station) {
    _selectStation(station);
  }

  // Get car icon using your custom PNG asset (like Google Maps live location)
  Future<BitmapDescriptor> _getCarIcon(
      {double rotation = 0.0, double pulseRadius = 0.0}) async {
    try {
      // Use your custom car marker PNG with pulsing green circle - like Google Maps live location
      // This supports rotation for navigation direction and pulsing animation
      return await _createCarIconFromAsset(
          rotation: rotation, pulseRadius: pulseRadius);
    } catch (e) {
      // Fallback to green marker
      return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  // Create car icon from your custom PNG asset with rotation support (like Google Maps)
  Future<BitmapDescriptor> _createCarIconFromAsset(
      {double rotation = 0.0, double pulseRadius = 0.0}) async {
    try {
      // Load your custom car marker PNG
      final ByteData data =
          await rootBundle.load('assets/icons/cars/car marker icons.png');
      final Uint8List bytes = data.buffer.asUint8List();

      // Decode the image with superior quality settings and higher target resolution
      final ui.Codec codec = await ui.instantiateImageCodec(
        bytes,
        allowUpscaling: false, // Prevent quality loss from upscaling
        targetWidth: 200, // Higher target resolution for better quality
        targetHeight: 280, // Maintain aspect ratio
      );
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image originalImage = frameInfo.image;

      // Resize to smaller size for better balance (36.0 x 50.0)
      final ui.Image resizedImage =
          await _resizeImage(originalImage, 36.0, 50.0);

      // Create a new image with rotation if needed
      final ui.Image rotatedImage;
      if (rotation != 0.0) {
        rotatedImage = await _rotateImage(resizedImage, rotation);
      } else {
        rotatedImage = resizedImage;
      }

      // Create final image with pulsing green circle background
      final ui.Image finalImage =
          await _addPulsingGreenCircle(rotatedImage, pulseRadius);

      // Convert to bytes for BitmapDescriptor with high quality PNG format
      final ByteData? finalByteData =
          await finalImage.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List finalBytes = finalByteData!.buffer.asUint8List();

      // Create BitmapDescriptor with high-resolution dimensions to accommodate pulsing circle
      return BitmapDescriptor.bytes(
        finalBytes,
        width: 128.0,
        height: 128.0,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Resize image to specific dimensions with superior quality preservation
  Future<ui.Image> _resizeImage(
      ui.Image image, double targetWidth, double targetHeight) async {
    // Use higher resolution for internal processing to maintain quality
    final double processingScale = 2.0; // 2x internal resolution
    final double internalWidth = targetWidth * processingScale;
    final double internalHeight = targetHeight * processingScale;

    // Create a high-resolution picture recorder
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Calculate scaling to fit within target dimensions while maintaining aspect ratio
    final double scaleX = internalWidth / image.width;
    final double scaleY = internalHeight / image.height;
    final double scale = math.min(scaleX, scaleY);

    // Calculate the actual size after scaling
    final double scaledWidth = image.width * scale;
    final double scaledHeight = image.height * scale;

    // Center the image within the target dimensions
    final double offsetX = (internalWidth - scaledWidth) / 2;
    final double offsetY = (internalHeight - scaledHeight) / 2;

    // Use superior quality paint settings for crisp rendering
    final Paint superiorQualityPaint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true
      ..style = PaintingStyle.fill;

    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      Rect.fromLTWH(offsetX, offsetY, scaledWidth, scaledHeight),
      superiorQualityPaint,
    );

    // Create high-resolution image first
    final ui.Picture picture = recorder.endRecording();
    final ui.Image highResImage =
        await picture.toImage(internalWidth.toInt(), internalHeight.toInt());

    // Now scale down to target size with high quality
    final ui.PictureRecorder finalRecorder = ui.PictureRecorder();
    final Canvas finalCanvas = Canvas(finalRecorder);

    finalCanvas.drawImageRect(
      highResImage,
      Rect.fromLTWH(0, 0, internalWidth, internalHeight),
      Rect.fromLTWH(0, 0, targetWidth, targetHeight),
      superiorQualityPaint,
    );

    final ui.Picture finalPicture = finalRecorder.endRecording();
    return await finalPicture.toImage(
        targetWidth.toInt(), targetHeight.toInt());
  }

  // Rotate image for navigation direction (like Google Maps live location)
  Future<ui.Image> _rotateImage(ui.Image image, double rotationDegrees) async {
    final double radians = rotationDegrees * (math.pi / 180);

    // Create a picture recorder to draw the rotated image
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Calculate the size needed for the rotated image
    final double width = image.width.toDouble();
    final double height = image.height.toDouble();

    // Move to center, rotate, then move back
    canvas.translate(width / 2, height / 2);
    canvas.rotate(radians);
    canvas.translate(-width / 2, -height / 2);

    // Draw the original image with high quality
    final Paint highQualityPaint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;
    canvas.drawImage(image, Offset.zero, highQualityPaint);

    // End recording and convert to image
    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(width.toInt(), height.toInt());
  }

  // Add pulsing green circle background (like Google Maps live location)
  Future<ui.Image> _addPulsingGreenCircle(
      ui.Image carImage, double pulseRadius) async {
    // Create a high-resolution canvas to accommodate the pulsing circle with superior quality
    final double canvasSize =
        128.0; // Much larger canvas for superior quality and pulse effect
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Calculate center position
    final Offset center = Offset(canvasSize / 2, canvasSize / 2);

    // Draw pulsing green circles (multiple layers for smooth effect)
    if (pulseRadius > 0) {
      // Outer pulse circle (more transparent)
      final Paint outerPulsePaint = Paint()
        ..color = const Color(0xFF4CAF50).withValues(alpha: 0.15)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, pulseRadius * 1.2, outerPulsePaint);

      // Middle pulse circle
      final Paint middlePulsePaint = Paint()
        ..color = const Color(0xFF4CAF50).withValues(alpha: 0.25)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, pulseRadius * 0.8, middlePulsePaint);

      // Inner pulse circle (more opaque)
      final Paint innerPulsePaint = Paint()
        ..color = const Color(0xFF4CAF50).withValues(alpha: 0.35)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(center, pulseRadius * 0.5, innerPulsePaint);
    }

    // Draw base green circle (always visible) - larger to fully contain car icon
    final Paint basePaint = Paint()
      ..color = const Color(0xFF4CAF50).withValues(alpha: 0.4)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, 28.0,
        basePaint); // Adjusted to 28.0 to properly contain the smaller car icon

    // Draw the car image in the center with high quality
    final double carWidth = carImage.width.toDouble();
    final double carHeight = carImage.height.toDouble();
    final Offset carPosition = Offset(
      center.dx - carWidth / 2,
      center.dy - carHeight / 2,
    );

    final Paint carPaint = Paint()
      ..filterQuality = FilterQuality.high
      ..isAntiAlias = true;
    canvas.drawImage(carImage, carPosition, carPaint);

    // End recording and convert to image
    final ui.Picture picture = recorder.endRecording();
    return await picture.toImage(canvasSize.toInt(), canvasSize.toInt());
  }

  // Get current location immediately and show car marker
  Future<void> _getCurrentLocationAndShowMarker() async {
    if (!_locationPermissionGranted) {
      return;
    }

    try {
      final position = await geo.Geolocator.getCurrentPosition(
        locationSettings: const geo.LocationSettings(
          accuracy: geo.LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      // Store initial position for bearing calculations
      _currentUserPosition = position;

      // Update the custom car marker immediately
      await _updateUserLocationMarker(position);
    } catch (e) {
      // Error getting location
    }
  }

  // Update user location marker with custom car icon and navigation rotation
  Future<void> _updateUserLocationMarker(geo.Position position) async {
    try {
      // Get car icon with current bearing rotation and pulse radius (like Google Maps live location)
      final carIcon = await _getCarIcon(
          rotation: _currentBearing, pulseRadius: _currentPulseRadius);

      // Create or update the user location marker
      final userMarker = Marker(
        markerId: const MarkerId('user_location'),
        position: LatLng(position.latitude, position.longitude),
        icon: carIcon,
        anchor: const Offset(0.5, 0.5), // Center the icon
        infoWindow: InfoWindow(
          title: 'Your Car Location',
          snippet: 'Current position • ${_currentBearing.toStringAsFixed(0)}°',
        ),
        zIndexInt: 1000, // Ensure it appears above other markers
        rotation: _currentBearing, // Additional rotation support if needed
      );

      if (mounted) {
        setState(() {
          _userLocationMarker = userMarker;
          _currentUserPosition = position;
        });
      }
    } catch (e) {
      // Error updating user location marker
    }
  }

  // Public method to reset auto-centering behavior (useful for location button)
  void resetAutoCentering() {
    _userHasInteractedWithMap = false;
    _hasShownInitialLocation = false;
  }

  // Method to fit map bounds with optimal padding for route visibility
  void _fitBounds(LatLngBounds bounds) async {
    if (_mapController != null) {
      try {
        debugPrint('📷 GOOGLE_MAP_WIDGET: Fitting bounds with optimal padding');
        debugPrint('📷 GOOGLE_MAP_WIDGET: Bounds: $bounds');

        // Use optimal padding for complete route visibility
        const double optimalPadding = 80.0;

        await _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(
            bounds,
            optimalPadding,
          ),
        );

        debugPrint(
            '📷 GOOGLE_MAP_WIDGET: ✅ Bounds fitting completed with ${optimalPadding}px padding');
      } catch (e) {
        debugPrint('📷 GOOGLE_MAP_WIDGET: ❌ Error fitting bounds: $e');
      }
    }
  }

  // Public method to fit bounds from external calls
  void fitBounds(LatLngBounds bounds) {
    _fitBounds(bounds);
  }

  // Get all markers (station markers + additional markers + custom user location marker)
  Set<Marker> _getAllMarkers() {
    final Set<Marker> allMarkers = Set<Marker>.from(_markers);

    // Add custom user location marker with green car icon
    if (_userLocationMarker != null) {
      allMarkers.add(_userLocationMarker!);
    }

    // Add additional markers (like trip location pins)
    if (widget.additionalMarkers != null) {
      allMarkers.addAll(widget.additionalMarkers!);
    }

    return allMarkers;
  }

  void _selectStation(Map<String, dynamic> station) {
    // Get the station ID
    final String id = station['id'].toString();

    // Update the selected station ID
    if (mounted) {
      setState(() {
        _selectedStationId = id;
      });
    }

    // Fly to the selected station's location
    final double latitude = station['latitude'] as double? ?? 0.0;
    final double longitude = station['longitude'] as double? ?? 0.0;

    // Focus on the selected station's location
    focusOnLocation(latitude, longitude, 15.0);

    // Update markers to show focused icon for selected station
    _updateMarkers();

    // Call the onStationSelected callback AFTER focusing on the location
    // This ensures the map is already centered when nearest stations are loaded
    if (widget.onStationSelected != null) {
      // Always try to enrich the station with UID, even if it already has one
      // This ensures we have the most up-to-date UID from the nearest stations API
      _enrichStationWithUid(station).then((enrichedStation) {
        widget.onStationSelected!(enrichedStation);
      });
    }
  }

  // Attempt to find a UID for a station based on its coordinates and other properties
  Future<Map<String, dynamic>> _enrichStationWithUid(
      Map<String, dynamic> station) async {
    try {
      final double latitude = station['latitude'] as double? ?? 0.0;
      final double longitude = station['longitude'] as double? ?? 0.0;
      final String stationName =
          (station['name'] as String? ?? '').toLowerCase();
      final String stationAddress =
          (station['address'] as String? ?? '').toLowerCase();
      final String stationId = station['id']?.toString() ?? '';

      if (latitude == 0.0 || longitude == 0.0) {
        debugPrint('Cannot enrich station - invalid coordinates');
        return station;
      }

      // Create a new map that we can modify
      final Map<String, dynamic> enrichedStation =
          Map<String, dynamic>.from(station);

      // Use the dashboard provider to get nearest stations that might have the UID
      final dashboardState = ref.read(dashboardNotifierProvider);
      final nearestStations = dashboardState.formattedNearestStations;

      if (nearestStations.isEmpty) {
        debugPrint('No nearest stations available for UID enrichment');
        return enrichedStation;
      }

      debugPrint(
          'Searching for UID match among ${nearestStations.length} nearest stations');

      // Log the first few nearest stations for debugging
      for (int i = 0; i < math.min(3, nearestStations.length); i++) {
        final s = nearestStations[i];
        debugPrint('Nearest station $i: name=${s['name']}, uid=${s['uid']}, '
            'lat=${s['latitude']}, lng=${s['longitude']}');
      }

      // Try multiple matching strategies in priority order

      // 0. First try matching by ID if available
      if (stationId.isNotEmpty) {
        var matchingStation = nearestStations.firstWhere(
          (s) =>
              s['id']?.toString() == stationId ||
              s['stationId']?.toString() == stationId,
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 1. Try exact coordinate match with wider threshold
      final double coordinateThreshold = 0.001; // About 100 meters
      var matchingStation = nearestStations.firstWhere(
        (s) {
          final stationLat = s['latitude'] as double? ?? 0.0;
          final stationLng = s['longitude'] as double? ?? 0.0;

          return (stationLat - latitude).abs() < coordinateThreshold &&
              (stationLng - longitude).abs() < coordinateThreshold;
        },
        orElse: () => {},
      );

      if (matchingStation.isNotEmpty) {
        _updateEnrichedStation(enrichedStation, matchingStation);
        return enrichedStation;
      }

      // 2. Try matching by name
      if (stationName.isNotEmpty) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final name = (s['name'] as String? ?? '').toLowerCase();
            return name.isNotEmpty &&
                (name == stationName ||
                    name.contains(stationName) ||
                    stationName.contains(name));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 3. Try matching by address
      if (stationAddress.isNotEmpty) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final address = (s['address'] as String? ?? '').toLowerCase();
            return address.isNotEmpty &&
                (address == stationAddress ||
                    address.contains(stationAddress) ||
                    stationAddress.contains(address));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 4. Try matching by partial address (if address is long enough)
      if (stationAddress.length > 10) {
        matchingStation = nearestStations.firstWhere(
          (s) {
            final address = (s['address'] as String? ?? '').toLowerCase();
            if (address.length < 10) return false;

            return address.contains(stationAddress.substring(0, 10)) ||
                stationAddress.contains(address.substring(0, 10));
          },
          orElse: () => {},
        );

        if (matchingStation.isNotEmpty) {
          _updateEnrichedStation(enrichedStation, matchingStation);
          return enrichedStation;
        }
      }

      // 5. Last resort: use the nearest station if it's reasonably close
      if (nearestStations.isNotEmpty) {
        final nearest = nearestStations.first;
        final nearestLat = nearest['latitude'] as double? ?? 0.0;
        final nearestLng = nearest['longitude'] as double? ?? 0.0;

        // Calculate distance between points (rough approximation)
        final latDiff = (nearestLat - latitude).abs();
        final lngDiff = (nearestLng - longitude).abs();
        final roughDistance = math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

        // If within ~5km (very rough approximation)
        if (roughDistance < 0.05) {
          debugPrint(
              'Using nearest station as last resort (rough distance: ${roughDistance * 111} km)');
          _updateEnrichedStation(enrichedStation, nearest);
          return enrichedStation;
        }
      }

      debugPrint('No matching station found with UID after all strategies');
      return enrichedStation;
    } catch (e) {
      debugPrint('Error enriching station with UID: $e');
      // Return the original station if there was an error
      return station;
    }
  }

  // Helper method to update enriched station with data from matching station
  void _updateEnrichedStation(Map<String, dynamic> enrichedStation,
      Map<String, dynamic> matchingStation) {
    // Copy UID
    if (matchingStation['uid'] != null &&
        matchingStation['uid'].toString().isNotEmpty) {
      final String uid = matchingStation['uid'].toString();
      enrichedStation['uid'] = uid;
    }

    // Copy other useful fields that might be missing
    final fieldsToCheck = [
      'stationId',
      'types',
      'connectors',
      'openingTimes',
      'openStatus',
      'mapPinUrl',
      'focusedMapPinUrl'
    ];

    for (final field in fieldsToCheck) {
      if (enrichedStation[field] == null && matchingStation[field] != null) {
        enrichedStation[field] = matchingStation[field];
        debugPrint('Copied missing field from matching station: $field');
      }
    }
  }

  // Handle location button press
  Future<void> handleLocationButtonPress() async {
    if (!_locationPermissionGranted) {
      await _checkLocationPermission();
      if (!_locationPermissionGranted) {
        return;
      }
    }

    if (mounted) {
      setState(() {
        _isLoadingLocation = true;
      });
    }

    try {
      final position = await geo.Geolocator.getCurrentPosition(
        locationSettings: const geo.LocationSettings(
          accuracy: geo.LocationAccuracy.high,
          timeLimit: Duration(seconds: 5),
        ),
      );

      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }

      // Reset user interaction flag when location button is explicitly pressed
      // This allows the map to center on user location when they request it
      _userHasInteractedWithMap = false;
      debugPrint(
          '🗺️ Location button pressed, temporarily enabling auto-centering');

      // Store position for bearing calculations
      _currentUserPosition = position;

      // Update custom user location marker with car icon
      await _updateUserLocationMarker(position);

      // Focus on user's current location
      await _focusOnLocation(position.latitude, position.longitude, 15.0);

      debugPrint(
          'Moved map to user location: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      debugPrint('Error getting current location: $e');
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }
    }
  }

  // Focus map on a specific location
  Future<void> _focusOnLocation(
      double latitude, double longitude, double zoom) async {
    if (_mapController != null) {
      await _mapController!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(latitude, longitude),
            zoom: zoom,
          ),
        ),
      );
    }
  }

  // Handle map tap for polyline selection
  void _handleMapTap(LatLng position) {
    if (widget.polylines == null || widget.polylines!.isEmpty) return;

    // Check if tap is near any polyline
    String? nearestPolylineId = _findNearestPolyline(position);

    if (nearestPolylineId != null && widget.onPolylineTapped != null) {
      widget.onPolylineTapped!(nearestPolylineId);
    }
  }

  // Find the nearest polyline to a tap position
  String? _findNearestPolyline(LatLng tapPosition) {
    if (widget.polylines == null) return null;

    const double tapThreshold =
        0.001; // Threshold for considering a tap "near" a polyline
    String? nearestPolylineId;
    double minDistance = double.infinity;

    for (final polyline in widget.polylines!) {
      // Skip shadow polylines
      if (polyline.polylineId.value.endsWith('_shadow')) continue;

      // Check distance to each point in the polyline
      for (final point in polyline.points) {
        final distance = _calculateDistance(tapPosition, point);

        if (distance < tapThreshold && distance < minDistance) {
          minDistance = distance;
          nearestPolylineId = polyline.polylineId.value;
        }
      }
    }

    return nearestPolylineId;
  }

  // Calculate distance between two LatLng points
  double _calculateDistance(LatLng point1, LatLng point2) {
    final double latDiff = point1.latitude - point2.latitude;
    final double lngDiff = point1.longitude - point2.longitude;
    return math.sqrt(latDiff * latDiff + lngDiff * lngDiff);
  }

  // Update markers on the map using PersistentMarkerService for reliability
  Future<void> _updateMarkers() async {
    if (widget.stations == null || widget.stations!.isEmpty) {
      if (mounted) {
        setState(() {
          _markers = {};
        });
      }
      return;
    }

    Set<Marker> newMarkers = {};

    // First, preload all marker URLs to ensure they're available before creating markers
    // This helps prevent disappearing icons during long map viewing sessions
    await _preloadAllMarkerIcons();

    // Process stations in smaller batches for mobile devices
    const int batchSize =
        5; // Increased batch size from 3 to 5 for better efficiency
    for (int i = 0; i < widget.stations!.length; i += batchSize) {
      final int end = (i + batchSize < widget.stations!.length)
          ? i + batchSize
          : widget.stations!.length;

      final batch = widget.stations!.sublist(i, end);

      // Process this batch of stations sequentially for better reliability
      for (final station in batch) {
        final double latitude = station['latitude'] as double? ?? 0.0;
        final double longitude = station['longitude'] as double? ?? 0.0;
        final String status = station['status'] as String? ?? 'Available';
        final String id = station['id'].toString();

        // Store UID if available (important for navigation to details page)
        final String? uid = station['uid']?.toString();

        if (latitude == 0.0 || longitude == 0.0) {
          continue;
        }

        try {
          // Use the persistent marker service to get reliable icons
          BitmapDescriptor markerIcon;

          try {
            // Determine if this is the selected station
            bool isSelected =
                _selectedStationId != null && _selectedStationId == id;

            // Get the appropriate marker URL with multiple fallback options
            String markerUrl = '';
            List<String> potentialUrls = [];

            // First try to use mapPinUrl from the station data
            if (station['mapPinUrl'] != null &&
                (station['mapPinUrl'] as String).isNotEmpty) {
              if (isSelected) {
                // For selected stations, prioritize focused versions
                if (station['focusedMapPinUrl'] != null &&
                    (station['focusedMapPinUrl'] as String).isNotEmpty) {
                  potentialUrls.add(station['focusedMapPinUrl'] as String);
                }

                // Add converted focused URL as fallback
                String defaultUrl = station['mapPinUrl'] as String;
                if (defaultUrl.contains('_default')) {
                  potentialUrls
                      .add(defaultUrl.replaceAll('_default', '_focus'));
                }

                // Add standard focus icon as last resort
                potentialUrls
                    .add('https://api2.eeil.online/mapicons/ecoplug_focus.png');
              } else {
                // For non-selected stations, use the provided mapPinUrl
                potentialUrls.add(station['mapPinUrl'] as String);
              }
            }

            // Add status-based fallback URLs
            if (status.toLowerCase().contains('unavailable')) {
              potentialUrls.add(
                  'https://api2.eeil.online/mapicons/ecoplug_unavailable.png');
            } else if (status.toLowerCase().contains('in use') ||
                status.toLowerCase().contains('charging')) {
              if (isSelected) {
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_focus_new.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_focus.png');
              } else {
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_default_new.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging_default.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_charging.png');
              }
            } else {
              if (isSelected) {
                potentialUrls
                    .add('https://api2.eeil.online/mapicons/ecoplug_focus.png');
              } else {
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_available.png');
                potentialUrls.add(
                    'https://api2.eeil.online/mapicons/ecoplug_default.png');
              }
            }

            // Try each URL in the potential URLs list until one works
            BitmapDescriptor? descriptor;
            for (final url in potentialUrls) {
              // Ensure URL starts with https://
              String normalizedUrl =
                  url.startsWith('http') ? url : 'https://$url';

              // Try to get the descriptor
              descriptor = await _persistentMarkerService
                  .getBitmapDescriptorFromUrl(normalizedUrl);
              if (descriptor != null) {
                markerUrl = normalizedUrl; // Remember which URL worked
                break;
              }
            }

            if (descriptor != null) {
              markerIcon = descriptor;
              // Log which URL was used
              debugPrint(
                  'Using marker icon from URL: $markerUrl for station $id');
            } else {
              // All URLs failed, use status-based fallback
              markerIcon = await _persistentMarkerService
                      .getMarkerDescriptorForStatus(status,
                          focused: isSelected,
                          // Force refresh if we're having failures
                          forceRefresh: true) ??
                  await _persistentMarkerService
                      .createCustomMarker(Colors.green);
              debugPrint('Using status-based fallback marker for station $id');
            }
          } catch (e) {
            debugPrint('Error creating marker icon for station $id: $e');
            // Use custom fallback markers instead of default Google markers
            try {
              // Create a custom marker programmatically based on status
              Color markerColor;
              if (status.toLowerCase().contains('unavailable')) {
                markerColor = Colors.red;
              } else if (status.toLowerCase().contains('in use') ||
                  status.toLowerCase().contains('charging')) {
                markerColor = Colors.blue;
              } else {
                markerColor = Colors.green;
              }

              // Use the persistent marker service to create a custom marker
              markerIcon = await _persistentMarkerService
                  .createCustomMarker(markerColor);
            } catch (fallbackError) {
              debugPrint(
                  'Error creating custom fallback marker: $fallbackError');
              // Last resort - create a minimal colored pixel marker
              final color = status.toLowerCase().contains('unavailable')
                  ? Colors.red
                  : Colors.green;
              markerIcon =
                  await _persistentMarkerService.createSimpleMarker(color);
            }
          }

          // Create marker with UID tag for navigation
          final marker = Marker(
            markerId: MarkerId(id),
            position: LatLng(latitude, longitude),
            icon: markerIcon,
            // Set anchor point for proper alignment
            anchor: const Offset(0.5, 0.5),
            infoWindow: InfoWindow(
              title: station['name'] as String? ?? 'Unknown Station',
              snippet:
                  '${station['distance']?.toStringAsFixed(2) ?? '0.0'} km • $status',
              onTap: () => _selectStation(station),
            ),
            onTap: () {
              _selectStation(station);
            },
            // Ensure we're passing the complete station data with UID
            consumeTapEvents: true,
          );

          // Add to markers set
          newMarkers.add(marker);
        } catch (e) {
          debugPrint('Error creating marker for station $id: $e');
        }

        // Small delay between processing each marker
        await Future.delayed(const Duration(
            milliseconds: 15)); // Reduced delay for better performance
      }

      // Update UI with available markers after each batch
      if (mounted) {
        setState(() {
          _markers = newMarkers;
        });

        // Delay between batches to let UI update
        await Future.delayed(const Duration(
            milliseconds: 50)); // Reduced delay for better performance
      } else {
        break; // Stop processing if widget is unmounted
      }
    }

    if (kDebugMode && _markers.isNotEmpty) {
      int count = 0;
      for (final marker in _markers) {
        count++;

        if (count >= 3) {
          break;
        }
      }
    }
  }

  // Preload all marker icons to ensure they don't disappear during long sessions
  Future<void> _preloadAllMarkerIcons() async {
    try {
      // Make sure the persistent marker service is initialized
      await _persistentMarkerService.initialize();

      // First, load the standard marker icon set
      await _persistentMarkerService.preloadCommonMarkers();

      // Then extract all unique marker URLs from stations
      final Set<String> allMarkerUrls = {};

      // Process all stations to collect marker URLs
      if (widget.stations != null) {
        for (final station in widget.stations!) {
          // Get standard pin URL
          if (station['mapPinUrl'] != null &&
              (station['mapPinUrl'] as String).isNotEmpty) {
            String url = station['mapPinUrl'] as String;
            if (!url.startsWith('http')) {
              url = 'https://$url';
            }
            allMarkerUrls.add(url);
          }

          // Get focused pin URL
          if (station['focusedMapPinUrl'] != null &&
              (station['focusedMapPinUrl'] as String).isNotEmpty) {
            String url = station['focusedMapPinUrl'] as String;
            if (!url.startsWith('http')) {
              url = 'https://$url';
            }
            allMarkerUrls.add(url);
          }
        }
      }

      // Preload in small batches
      final List<String> urlList = allMarkerUrls.toList();

      const int preloadBatchSize = 3;
      for (int i = 0; i < urlList.length; i += preloadBatchSize) {
        final int end = (i + preloadBatchSize < urlList.length)
            ? i + preloadBatchSize
            : urlList.length;

        final batch = urlList.sublist(i, end);

        // Load images and descriptors in parallel for this small batch
        await Future.wait(batch.map((url) async {
          // Load image first
          await _persistentMarkerService.getMarkerImage(url);
          // Then create descriptors at different sizes
          await _persistentMarkerService.getBitmapDescriptorFromUrl(url);
        }));

        // Short delay between batches
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Get cache statistics
      final stats = _persistentMarkerService.getCacheStatistics();
      debugPrint(
          'Marker cache status: ${stats['imageCount']} images, ${stats['descriptorCount']} descriptors');
    } catch (e) {
      debugPrint('Error preloading marker icons: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen for theme changes
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final currentIsDarkMode = themeNotifier.isDarkMode;

    // Update map theme if it changed
    if (currentIsDarkMode != _isDarkMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateMapTheme(currentIsDarkMode);
      });
    }

    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              GoogleMap(
                initialCameraPosition: CameraPosition(
                  target: LatLng(widget.initialLatitude ?? 20.5937,
                      widget.initialLongitude ?? 78.9629),
                  zoom: widget.initialZoom ?? 5.0,
                ),
                style: _currentMapStyle, // Apply the current map style
                myLocationEnabled:
                    false, // Disable default location marker - using custom car icon
                myLocationButtonEnabled: false,
                zoomControlsEnabled: false,
                markers: _getAllMarkers(),
                polylines: () {
                  final polylines = widget.polylines ?? <Polyline>{};

                  if (polylines.isNotEmpty) {
                    for (final polyline in polylines) {}
                  } else {}
                  return polylines;
                }(),
                onMapCreated: (GoogleMapController controller) {
                  _mapController = controller;
                  _controller.complete(controller);
                  if (mounted) {
                    setState(() {
                      _isMapInitialized = true;
                    });
                  }

                  // Initialize markers once map is ready
                  _updateMarkers();

                  // Initialize user location marker if permission is granted
                  if (_locationPermissionGranted) {
                    _startLocationTracking();
                    _getCurrentLocationAndShowMarker();
                  }

                  // Enhanced polyline rendering with multiple delays

                  // Force immediate rebuild to ensure polylines are included
                  if (mounted) {
                    Future.delayed(const Duration(milliseconds: 100), () {
                      if (mounted) {
                        setState(() {});
                      }
                    });
                  }

                  // Additional rebuild after 500ms
                  if (mounted) {
                    Future.delayed(const Duration(milliseconds: 500), () {
                      if (mounted) {
                        setState(() {});
                      }
                    });
                  }

                  // Fit bounds if provided - immediate fitting for optimal route framing
                  if (widget.fitBounds != null) {
                    debugPrint(
                        '📷 GOOGLE_MAP_WIDGET: Route bounds provided, initiating auto-framing');

                    // Immediate bounds fitting for responsive auto-zoom
                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (mounted) {
                        debugPrint(
                            '📷 GOOGLE_MAP_WIDGET: Executing immediate route auto-framing');
                        _fitBounds(widget.fitBounds!);
                      }
                    });

                    // Secondary bounds fitting to ensure optimal framing
                    Future.delayed(const Duration(milliseconds: 800), () {
                      if (mounted) {
                        debugPrint(
                            '📷 GOOGLE_MAP_WIDGET: Executing secondary route auto-framing for optimal view');
                        _fitBounds(widget.fitBounds!);
                      }
                    });
                  }

                  // Final polyline check after 1 second
                  Future.delayed(const Duration(milliseconds: 1000), () {
                    if (mounted && widget.polylines != null) {
                      if (widget.polylines!.isNotEmpty) {
                        for (final polyline in widget.polylines!) {}
                      } else {
                        debugPrint(
                            '🗺️ GOOGLE MAP WIDGET: ❌ No polylines available for display');
                      }
                    }
                  });
                },
                onTap: (LatLng position) {
                  // Mark that user has interacted with the map
                  _userHasInteractedWithMap = true;
                  debugPrint('🗺️ User tapped map, disabling auto-centering');

                  // Check if tap is near any polyline for route selection
                  _handleMapTap(position);

                  // Call the original onTap callback
                  if (widget.onTap != null) {
                    widget.onTap!(position);
                  }
                },
                onCameraMove: (CameraPosition position) {
                  // Mark that user has interacted with the map (manual zoom/pan)
                  if (!_userHasInteractedWithMap) {
                    _userHasInteractedWithMap = true;
                  }

                  // Notify parent of camera position changes if callback is provided
                  if (widget.onCameraPositionChanged != null) {
                    widget.onCameraPositionChanged!(position);
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
